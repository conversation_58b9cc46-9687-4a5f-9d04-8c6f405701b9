<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="14.2">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>60</x>
      <y>30</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>_:Client_</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>90</x>
      <y>50</y>
      <w>30</w>
      <h>600</h>
    </coordinates>
    <panel_attributes>lt=.</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;580.0</additional_attributes>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>260</x>
      <y>30</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>_:POS Proxy_</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>520</x>
      <y>30</y>
      <w>200</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>_:gate.payneteasy.com_</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>90</x>
      <y>110</y>
      <w>230</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;&lt;-
POST /pos-proxy/pay</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>290</x>
      <y>50</y>
      <w>30</w>
      <h>240</h>
    </coordinates>
    <panel_attributes>lt=.</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;220.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>600</x>
      <y>50</y>
      <w>30</w>
      <h>590</h>
    </coordinates>
    <panel_attributes>lt=.</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;570.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>90</x>
      <y>320</y>
      <w>540</w>
      <h>110</h>
    </coordinates>
    <panel_attributes>lt=&lt;&lt;-
POST https://gate.payneteasy.com/paynet/api/v2/status
with Order ID

see http://doc.payneteasy.com/card_payment_API/
sale-transactions.html#order-status
</panel_attributes>
    <additional_attributes>520.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>90</x>
      <y>470</y>
      <w>540</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;&lt;-
Responses with Order Status</panel_attributes>
    <additional_attributes>10.0;20.0;520.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>90</x>
      <y>120</y>
      <w>20</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>
bg=yellow</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>290</x>
      <y>110</y>
      <w>20</w>
      <h>120</h>
    </coordinates>
    <panel_attributes>
bg=yellow</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>90</x>
      <y>170</y>
      <w>230</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;&lt;-
Returns Order ID
</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>90</x>
      <y>320</y>
      <w>20</w>
      <h>200</h>
    </coordinates>
    <panel_attributes>
bg=yellow</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>600</x>
      <y>320</y>
      <w>20</w>
      <h>190</h>
    </coordinates>
    <panel_attributes>
bg=yellow</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLGeneric</id>
    <coordinates>
      <x>1100</x>
      <y>40</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>_:Merchant Site_</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1140</x>
      <y>60</y>
      <w>30</w>
      <h>560</h>
    </coordinates>
    <panel_attributes>lt=.</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;540.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>600</x>
      <y>260</y>
      <w>570</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
Makes a callback
see http://doc.payneteasy.com/common_utilities/merchants_callbacks.html</panel_attributes>
    <additional_attributes>550.0;20.0;10.0;20.0</additional_attributes>
  </element>
</diagram>

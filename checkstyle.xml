<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.2//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_2.dtd">
<module name="Checker">

    <module name="SuppressionFilter">
        <property name="file" value="./checkstyle-suppressions.xml"/>
    </module>

    <module name="Translation"/>

    <module name="FileLength"/>

    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>


    <module name="TreeWalker">

        <property name="cacheFile" value="${checkstyle.cache.file}"/>

        <!-- Checks for Naming Conventions.                  -->
        <!-- See http://checkstyle.sf.net/config_naming.html -->
        <module name="ConstantName">
            <property name="format" value="^([A-Z_][A-Z0-9]*(_[A-Z0-9]+)*|_log)$"/>
        </module>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>


        <!-- Checks for Headers                                -->
        <!-- See http://checkstyle.sf.net/config_header.html   -->
        <!-- <module name="Header">                            -->
        <!-- The follow property value demonstrates the ability     -->
        <!-- to have access to ANT properties. In this case it uses -->
        <!-- the ${basedir} property to allow Checkstyle to be run  -->
        <!-- from any directory within a project. See property      -->
        <!-- expansion,                                             -->
        <!-- http://checkstyle.sf.net/config.html#properties        -->
        <!-- <property                                              -->
        <!--     name="headerFile"                                  -->
        <!--     value="${basedir}/java.header"/>                   -->
        <!-- </module> -->

        <!-- Following interprets the header file as regular expressions. -->
        <!-- <module name="RegexpHeader"/>                                -->


        <!-- Checks for imports                              -->
        <!-- See http://checkstyle.sf.net/config_import.html -->
        <!--module name="AvoidStarImport"/-->
        <module name="IllegalImport"/>
        <!-- defaults to sun.* packages -->
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>


        <!-- Checks for Size Violations.                    -->
        <!-- See http://checkstyle.sf.net/config_sizes.html -->
        <module name="LineLength">
            <property name="max" value="160"/>
        </module>
        <module name="MethodLength"/>
        <module name="ParameterNumber">
            <property name="max" value="7"/>
        </module>

        <!-- Checks for whitespace                               -->
        <!-- See http://checkstyle.sf.net/config_whitespace.html -->
        <module name="EmptyForIteratorPad"/>
        <!--<module name="MethodParamPad"/>-->
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore">
            <property name="allowLineBreaks" value="true"/>
        </module>
        <module name="OperatorWrap"/>
        <!--<module name="ParenPad"/>-->
        <!--<module name="TypecastParenPad"/>-->
        <module name="WhitespaceAfter"/>
        <!--module name="WhitespaceAround"/-->


        <!-- Modifier Checks                                    -->
        <!-- See http://checkstyle.sf.net/config_modifiers.html -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>


        <!-- Checks for blocks. You know, those {}'s         -->
        <!-- See http://checkstyle.sf.net/config_blocks.html -->
        <module name="AvoidNestedBlocks">
            <property name="allowInSwitchCase" value="true"/>
        </module>
        <module name="EmptyBlock"/>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>


        <!-- Checks for common coding problems               -->
        <!-- See http://checkstyle.sf.net/config_coding.html -->
        <!--module name="AvoidInlineConditionals"/-->
        <!-- MY FAVOURITE -->
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <!--module name="HiddenField"/-->
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <!--module name="MagicNumber"/-->
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="StringLiteralEquality"/>
        <module name="NestedIfDepth"/>
        <module name="NestedTryDepth">
            <property name="max" value="3"/>
        </module>
        <module name="SuperClone"/>
        <module name="IllegalCatch">
            <property name="illegalClassNames" value=" java.lang.Throwable, java.lang.RuntimeException"/>
        </module>
        <!--module name="IllegalThrows"/-->
        <module name="FallThrough"/>
        <module name="UnnecessaryParentheses"/>
        <!--module name="FinalLocalVariable"/-->

        <!-- Checks for class design                         -->
        <!-- See http://checkstyle.sf.net/config_design.html -->
        <!--module name="DesignForExtension"/-->
        <module name="FinalClass"/>
        <!--module name="HideUtilityClassConstructor"/-->
        <module name="InterfaceIsType"/>
        <!--<module name="VisibilityModifier"/>-->


        <!-- Miscellaneous other checks.                   -->
        <!-- See http://checkstyle.sf.net/config_misc.html -->
        <module name="ArrayTypeStyle"/>
        <module name="Regexp">
            <property name="format" value="System\.(out)|(err)\.print(ln)?\("/>
            <property name="illegalPattern" value="true"/>
        </module>
        <module name="Regexp">
            <property name="format" value="System\.exit"/>
            <property name="illegalPattern" value="true"/>
        </module>
        <module name="Regexp">
            <property name="format" value="\.printStacktrace"/>
            <property name="illegalPattern" value="true"/>
        </module>

        <!--module name="FinalParameters"/-->
        <!--module name="GenericIllegalRegexp">
            <property name="format" value="\s+$"/>
            <property name="message" value="Line has trailing spaces."/>
        </module-->
        <!--module name="TodoComment"/-->
        <module name="UpperEll"/>

    </module>

</module>

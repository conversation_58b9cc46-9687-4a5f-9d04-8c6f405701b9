version: 2
jobs:
  build:
    docker:
      - image: circleci/openjdk:8-jdk

    working_directory: ~/repo

    environment:
      MAVEN_OPTS: -Xmx3200m

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
          - v1-dependencies-{{ checksum "pom.xml" }}
          # fallback to using the latest cache if no exact match is found
          - v1-dependencies-

      - run: mvn test

      - save_cache:
          paths:
            - ~/.m2
          key: v1-dependencies-{{ checksum "pom.xml" }}

      # run tests!
      - run: ./mvnw clean install

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <artifactId>pos-proxy</artifactId>
    <groupId>com.payneteasy</groupId>
    <version>1.3-SNAPSHOT</version>

    <name>pos robot ${project.version}</name>

    <description>POS/mPOS robot for passing ceritification</description>

    <url>https://github.com/evsinev/pos-proxy</url>

    <modules>
        <module>server</module>
        <module>integration-testing</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <logback.version> 1.1.11  </logback.version>
        <slf4j.version>   1.7.25 </slf4j.version>
        <grpc.version>    1.8.0  </grpc.version>

        <jacoco.basedir>${project.basedir}/../target/coverage</jacoco.basedir>

        <jacoco.version>0.8.0</jacoco.version>

        <sonar.jacoco.reportPaths>${project.basedir}/../target/jacoco.exec</sonar.jacoco.reportPaths>
        <sonar.language>java</sonar.language>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>

        <android.version>*******</android.version>

        <reader.version>1.4-90</reader.version>
    </properties>

    <scm>
        <connection>scm:git:**************:evsinev/pos-proxy.git</connection>
        <developerConnection>scm:git:**************:evsinev/pos-proxy.git</developerConnection>
        <url>https://github.com/evsinev/pos-proxy.git</url>
        <tag>HEAD</tag>
  </scm>

   <repositories>
        <repository>
            <id>reader-repo</id>
            <name>reader repo</name>
            <url>http://paynet-qa.clubber.me/reader/maven</url>
        </repository>

        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>

    </repositories>

    <!--
        -      Distributions
        -->
    <distributionManagement>

        <repository>
            <id>sonatype-nexus-staging</id>
            <name>sonatype oss RELEASE repository</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
        </repository>

        <snapshotRepository>
            <id>sonatype-nexus-snapshots</id>
            <name>sonatype oss SNAPSHOT repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>

        <site>
            <id>local-site</id>
            <name>Store only on local disk</name>
            <url>file:///tmp/mysql-scheduler-site</url>
        </site>
    </distributionManagement>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>esinev</id>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.payneteasy.pos-proxy</groupId>
                <artifactId>server</artifactId>
                <version>1.3-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.payneteasy.android.reader.readers</groupId>
                <artifactId>readers-miura-rxtx</artifactId>
                <version>${reader.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.payneteasy.android.reader.readers</groupId>
                <artifactId>readers-inpas</artifactId>
                <version>${reader.version}</version>
            </dependency>

            <dependency>
                <groupId>com.payneteasy.android.reader.readers</groupId>
                <artifactId>readers-common-rxtx-network</artifactId>
                <version>${reader.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.android</groupId>
                <artifactId>android</artifactId>
                <version>${android.version}</version>
                <scope>provided</scope>
            </dependency>

            <!--  Gson: Java to Json conversion -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.2.4</version>
            </dependency>

            <dependency>
                <groupId>ru.odnoklassniki</groupId>
                <artifactId>one-nio</artifactId>
                <version>1.0.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>tools</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>

            <!-- SLF4J -->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>15.0</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>1.10.19</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.12</version>
                <scope>provided</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <showWarnings>true</showWarnings>
                    <showDeprecation>true</showDeprecation>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <formats>
                        <format>html</format>
                        <format>xml</format>
                    </formats>
                    <aggregate>true</aggregate>
                    <check />
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <destFile>${sonar.jacoco.reportPaths}</destFile>
                    <dataFile>${sonar.jacoco.reportPaths}</dataFile>
                    <append>true</append>
                </configuration>
                <executions>
                    <execution>
                        <id>agent-for-ut</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <configuration>
                            <configLocation>checkstyle.xml</configLocation>
                            <encoding>UTF-8</encoding>
                            <consoleOutput>true</consoleOutput>
                            <failsOnError>true</failsOnError>
                        </configuration>
                        <!--<goals>-->
                            <!--<goal>check</goal>-->
                        <!--</goals>-->
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.3</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>sonatype-nexus-staging</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <skip>true</skip>
                    <skipDeploy>true</skipDeploy>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <reporting>
        <plugins>
            <!-- disable all reports for quick release -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.7</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>summary</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <!-- signs artifacts. This profile will be activated when the value of maven property performRelease is true -->
    <profiles>
        <profile>
            <id>release-profile</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>

                    <!-- SIGNS -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
